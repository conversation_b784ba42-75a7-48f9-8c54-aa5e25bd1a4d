t=2025-06-04T13:42:24-0300 lvl=info msg="no configuration paths supplied"
t=2025-06-04T13:42:24-0300 lvl=info msg="using configuration at default config path" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml"
t=2025-06-04T13:42:24-0300 lvl=info msg="open config file" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml" err=nil
t=2025-06-04T13:42:24-0300 lvl=info msg="starting web service" obj=web addr=127.0.0.1:4040 allow_hosts=[]
t=2025-06-04T13:42:24-0300 lvl=info msg="client session established" obj=tunnels.session
t=2025-06-04T13:42:24-0300 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-06-04T13:42:24-0300 lvl=info msg="started tunnel" obj=tunnels name=command_line addr=http://localhost:3000 url=https://5cb0-200-194-249-22.ngrok-free.app
t=2025-06-04T13:42:25-0300 lvl=info msg="update available" obj=updater
t=2025-06-04T13:42:29-0300 lvl=info msg=start pg=/api/tunnels id=71f9dd850ff8aa6d
t=2025-06-04T13:42:29-0300 lvl=info msg=end pg=/api/tunnels id=71f9dd850ff8aa6d status=200 dur=652.375µs
t=2025-06-04T13:42:29-0300 lvl=info msg="join connections" obj=join id=72957f428647 l=[::1]:3000 r=**************:61283
