t=2025-06-04T13:42:24-0300 lvl=info msg="no configuration paths supplied"
t=2025-06-04T13:42:24-0300 lvl=info msg="using configuration at default config path" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml"
t=2025-06-04T13:42:24-0300 lvl=info msg="open config file" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml" err=nil
t=2025-06-04T13:42:24-0300 lvl=info msg="starting web service" obj=web addr=127.0.0.1:4040 allow_hosts=[]
t=2025-06-04T13:42:24-0300 lvl=info msg="client session established" obj=tunnels.session
t=2025-06-04T13:42:24-0300 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-06-04T13:42:24-0300 lvl=info msg="started tunnel" obj=tunnels name=command_line addr=http://localhost:3000 url=https://5cb0-200-194-249-22.ngrok-free.app
t=2025-06-04T13:42:25-0300 lvl=info msg="update available" obj=updater
t=2025-06-04T13:42:29-0300 lvl=info msg=start pg=/api/tunnels id=71f9dd850ff8aa6d
t=2025-06-04T13:42:29-0300 lvl=info msg=end pg=/api/tunnels id=71f9dd850ff8aa6d status=200 dur=652.375µs
t=2025-06-04T13:42:29-0300 lvl=info msg="join connections" obj=join id=72957f428647 l=[::1]:3000 r=**************:61283
t=2025-06-04T13:49:05-0300 lvl=info msg="join connections" obj=join id=1d9730c1c7fa l=[::1]:3000 r=*************:43308
t=2025-06-04T13:49:07-0300 lvl=info msg="join connections" obj=join id=2b5fca2bfb01 l=[::1]:3000 r=************:56902
t=2025-06-04T13:49:35-0300 lvl=info msg="join connections" obj=join id=42653dfd63a6 l=[::1]:3000 r=************:40106
t=2025-06-04T13:52:31-0300 lvl=info msg="received stop request" obj=app stopReq="{err:<nil> restart:false}"
t=2025-06-04T13:52:31-0300 lvl=info msg="session closing" obj=tunnels.session err=nil
t=2025-06-04T13:52:31-0300 lvl=info msg="accept failed" obj=tunnels.session obj=csess id=fca37feed0b4 err="reconnecting session closed"
t=2025-06-04T13:52:31-0300 lvl=info msg="failed to accept connection: Listener closed" obj=tunnels.session clientid=7e71ad8d013a33c15d11ed0a59100f1f
