<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Twilio WhatsApp Chat System</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://sdk.twilio.com/js/conversations/v2.4/twilio-conversations.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>💬 Twilio WhatsApp Chat System</h1>
            <p>Sistema de chat reativo - Aguardando mensagens dos usuários</p>
        </header>

        <div class="main-content">
            <!-- Seção de Status do Sistema -->
            <section class="system-status-section">
                <h2>📊 Status do Sistema</h2>

                <div class="status-grid">
                    <div class="status-card">
                        <h3>🔗 Conexão Twilio</h3>
                        <div class="status" id="connectionStatus">
                            <span class="status-indicator offline"></span>
                            <span class="status-text">Desconectado</span>
                        </div>
                        <button id="connectBtn" class="btn btn-primary">Conectar como Operador</button>
                    </div>

                    <div class="status-card">
                        <h3>📱 WhatsApp Sender</h3>
                        <div class="status" id="whatsappStatus">
                            <span class="status-indicator online"></span>
                            <span class="status-text">+18382700077 Ativo</span>
                        </div>
                        <small>Sistema aguardando mensagens dos usuários</small>
                    </div>

                    <div class="status-card">
                        <h3>🌐 Webhooks</h3>
                        <div class="status" id="webhookStatus">
                            <span class="status-indicator connecting"></span>
                            <span class="status-text">Verificando...</span>
                        </div>
                        <button id="checkWebhooksBtn" class="btn btn-secondary">Verificar</button>
                    </div>
                </div>

                <div class="config-group" style="margin-top: 20px;">
                    <label for="userIdentity">Identidade do Operador:</label>
                    <input type="text" id="userIdentity" placeholder="operador-1" value="operador-1">
                </div>
            </section>

            <!-- Seção de Conversas Ativas -->
            <section class="active-conversations-section">
                <h2>💬 Conversas Ativas</h2>
                <div class="conversations-header">
                    <button id="refreshConversationsBtn" class="btn btn-secondary">🔄 Atualizar</button>
                    <span id="conversationsCount">0 conversas ativas</span>
                </div>
                <div class="conversations-list" id="conversationsList">
                    <div class="no-conversations">
                        <p>📭 Nenhuma conversa ativa</p>
                        <small>O sistema está aguardando mensagens dos usuários para criar conversas automaticamente</small>
                    </div>
                </div>
            </section>

            <!-- Seção de Chat -->
            <section class="chat-section">
                <h2>💭 Chat Ativo</h2>

                <div class="chat-header" id="chatHeader" style="display: none;">
                    <div class="chat-info">
                        <h3 id="activeChatTitle">Selecione uma conversa</h3>
                        <small id="activeChatDetails">Nenhuma conversa selecionada</small>
                    </div>
                    <div class="chat-actions">
                        <button id="closeChatBtn" class="btn btn-secondary">✕ Fechar</button>
                    </div>
                </div>

                <div class="chat-container">
                    <div class="messages" id="messagesContainer">
                        <div class="message system">
                            <p>👋 Sistema de chat reativo ativo!</p>
                            <p>Aguardando mensagens dos usuários para iniciar conversas...</p>
                        </div>
                    </div>

                    <div class="typing-indicator" id="typingIndicator" style="display: none;">
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="typing-text">Usuário está digitando...</span>
                    </div>

                    <div class="message-input">
                        <input type="text" id="messageInput" placeholder="Selecione uma conversa para responder..." disabled>
                        <button id="sendBtn" class="btn btn-primary" disabled>Enviar</button>
                    </div>
                </div>
            </section>

            <!-- Seção de Logs -->
            <section class="logs-section">
                <h2>📝 Logs do Sistema</h2>
                <div class="logs-controls">
                    <button id="clearLogsBtn" class="btn btn-secondary">Limpar Logs</button>
                    <button id="refreshLogsBtn" class="btn btn-secondary">Atualizar Logs</button>
                    <button id="toggleAutoRefreshBtn" class="btn btn-secondary">Auto-refresh: OFF</button>
                </div>
                <div class="logs" id="logsContainer">
                    <div class="log-entry">
                        <span class="timestamp">[Sistema iniciado]</span>
                        <span class="log-message">Aguardando conexão do operador...</span>
                    </div>
                </div>
            </section>
        </div>

        <!-- Instruções -->
        <section class="instructions">
            <h2>📖 Como Funciona o Sistema Reativo</h2>
            <div class="instruction-grid">
                <div class="instruction-card">
                    <h3>🔄 Sistema Reativo</h3>
                    <ol>
                        <li><strong>Aguarda Mensagens:</strong> O sistema fica "de prontidão" esperando mensagens dos usuários</li>
                        <li><strong>Criação Automática:</strong> Quando um usuário envia a primeira mensagem, uma conversa é criada automaticamente</li>
                        <li><strong>Notificação:</strong> O operador é notificado sobre a nova conversa</li>
                        <li><strong>Resposta:</strong> O operador pode responder através da interface web</li>
                    </ol>
                </div>

                <div class="instruction-card">
                    <h3>👨‍💼 Para Operadores</h3>
                    <ol>
                        <li><strong>Conectar:</strong> Clique em "Conectar como Operador"</li>
                        <li><strong>Monitorar:</strong> Acompanhe as conversas ativas na lista</li>
                        <li><strong>Responder:</strong> Clique em uma conversa para abrir o chat</li>
                        <li><strong>Typing Indicator:</strong> Digite para mostrar que está respondendo</li>
                    </ol>
                </div>
            </div>

            <div class="warning">
                <h3>📱 Configuração WhatsApp:</h3>
                <ul>
                    <li>✅ WhatsApp Sender: +18382700077 (registrado, sem limitações)</li>
                    <li>✅ Webhooks configurados via ngrok para receber mensagens</li>
                    <li>✅ Conversas iniciadas pelos usuários finais (sem templates)</li>
                    <li>✅ Sistema totalmente reativo e automático</li>
                </ul>
            </div>
        </section>
    </div>

    <script src="script.js"></script>
</body>
</html>
