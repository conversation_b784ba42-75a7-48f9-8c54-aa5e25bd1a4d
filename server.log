
> twilio-whatsapp-chat-system@2.0.0 start
> node server.js

🚀 Servidor rodando na porta 3000
📱 Acesse: http://localhost:3000
🔧 Ambiente: development
✅ Todas as variáveis de ambiente configuradas
🔚 Encerrando todas as conversas ativas...
📋 Encontradas 0 conversas ativas
🎉 0 conversas encerradas com sucesso
[2025-06-04T18:05:24.134Z] WEBHOOK_RECEIVED: {
  method: 'POST',
  url: '/whatsapp/incoming',
  headers: {
    'content-type': 'application/x-www-form-urlencoded',
    'user-agent': 'curl/8.7.1',
    'x-twilio-signature': undefined
  },
  body: {
    From: 'whatsapp: *************',
    To: 'whatsapp: ***********',
    Body: 'Oi, preciso de ajuda',
    MessageSid: 'TEST_AUTO_1749060324',
    AccountSid: 'AC04266eaa4a7caf821e8dac9d92879e95',
    ApiVersion: '2010-04-01'
  },
  query: {}
}
[2025-06-04T18:05:24.135Z] MESSAGE_RECEIVED: {
  messageSid: 'TEST_AUTO_1749060324',
  from: 'whatsapp: *************',
  to: 'whatsapp: ***********',
  body: 'Oi, preciso de ajuda',
  conversationSid: undefined,
  participantSid: undefined,
  author: undefined
}
📱 Mensagem WhatsApp recebida: {
  from: 'whatsapp: *************',
  to: 'whatsapp: ***********',
  body: 'Oi, preciso de ajuda',
  conversationSid: undefined
}
🔄 Criando conversa automática para: whatsapp: *************
🔍 Número formatado: whatsapp:*************
📝 Criando nova conversa: Chat WhatsApp ************* - 6/4/2025, 3:05:31 PM
👤 Adicionando participante WhatsApp: whatsapp:*************
🔧 Tentando criar participante com Número Registrado:
   Address: whatsapp:*************
   Proxy: whatsapp:+***********
   Conversation: CH9276f12173d24c83977fd6675cb10f28
❌ Falha com Número Registrado: Invalid messaging binding address
🔧 Tentando criar participante com Sandbox (Fallback):
   Address: whatsapp:*************
   Proxy: whatsapp:+14155238886
   Conversation: CH9276f12173d24c83977fd6675cb10f28
❌ Falha com Sandbox (Fallback): Invalid messaging binding address
Erro ao criar conversa automática: Error: Falha ao criar participante com todos os proxies disponíveis
    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:207:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:212:9)
    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:264:17
Erro ao criar conversa automática: Error: Falha ao criar participante com todos os proxies disponíveis
    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:207:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:212:9)
    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:264:17
[2025-06-04T18:05:32.445Z] ERROR: {
  message: 'Erro ao criar conversa automática',
  stack: 'Error: Falha ao criar participante com todos os proxies disponíveis\n' +
    '    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:207:19)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:212:9)\n' +
    '    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:264:17',
  context: { from: 'whatsapp: *************', body: 'Oi, preciso de ajuda' }
}
